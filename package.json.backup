{"name": "f<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "author": "青春年华", "description": "杭州青春年华文旅发展有限公司收银系统", "license": "MIT", "main": "./dist/electron/main.js", "scripts": {"dev": "cross-env TERGET_ENV=development node .electron-vue/dev-runner.js", "build": "cross-env BUILD_TARGET=clean CSC_IDENTITY_AUTO_DISCOVERY=false node .electron-vue/build.js  && electron-builder", "build:win32": "cross-env BUILD_TARGET=clean node .electron-vue/build.js  && electron-builder --win  --ia32", "build:win64": "cross-env BUILD_TARGET=clean node .electron-vue/build.js  && electron-builder --win  --x64", "build:mac": "cross-env BUILD_TARGET=clean node .electron-vue/build.js  && electron-builder --mac", "build:dir": "cross-env BUILD_TARGET=clean CSC_IDENTITY_AUTO_DISCOVERY=false node .electron-vue/build.js && electron-builder --dir", "build:clean": "cross-env BUILD_TARGET=onlyClean node .electron-vue/build.js", "build:web": "cross-env BUILD_TARGET=web node .electron-vue/build.js", "build:portable": "cross-env BUILD_TARGET=clean node .electron-vue/build.js && node build-portable.js", "build:installer": "cross-env BUILD_TARGET=clean CSC_IDENTITY_AUTO_DISCOVERY=false node .electron-vue/build.js && electron-builder --win nsis", "pack:resources": "node .electron-vue/hot-updater.js", "update:serve": "node server/index.js", "dep:upgrade": "yarn upgrade-interactive --latest", "postinstall": "electron-builder install-app-deps"}, "build": {"asar": false, "extraFiles": [], "publish": [{"provider": "generic", "url": "http://127.0.0.1"}], "productName": "68U选", "appId": "cn.fuint.cashier", "directories": {"output": "build"}, "files": ["dist/electron/**/*"], "dmg": {"contents": [{"x": 410, "y": 150, "type": "link", "path": "/Applications"}, {"x": 130, "y": 150, "type": "file"}]}, "mac": {"icon": "build/icons/icon.icns"}, "win": {"icon": "build/icons/icon.ico", "target": [{"target": "portable", "arch": ["x64"]}, {"target": "nsis", "arch": ["x64"]}], "sign": false, "verifyUpdateCodeSignature": false}, "linux": {"target": "deb", "icon": "build/icons"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "68U选", "installerIcon": "build/icons/icon.ico", "uninstallerIcon": "build/icons/icon.ico", "installerHeaderIcon": "build/icons/icon.ico", "deleteAppDataOnUninstall": false, "runAfterFinish": true, "menuCategory": "68U选收银系统"}}, "dependencies": {"axios": "^1.9.0", "clipboard": "2.0.8", "electron-updater": "^5.3.0", "express": "4.18.2", "fs-extra": "^11.1.0", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "serialport": "^13.0.0", "vue-print-nb": "^1.7.5"}, "devDependencies": {"@babel/core": "^7.22.5", "@babel/eslint-parser": "^7.22.5", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.22.5", "@babel/plugin-proposal-do-expressions": "^7.22.5", "@babel/plugin-proposal-export-default-from": "^7.22.5", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-function-bind": "^7.22.5", "@babel/plugin-proposal-function-sent": "^7.22.5", "@babel/plugin-proposal-json-strings": "^7.18.6", "@babel/plugin-proposal-logical-assignment-operators": "^7.20.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-numeric-separator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-proposal-pipeline-operator": "^7.22.5", "@babel/plugin-proposal-throw-expressions": "^7.22.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-transform-runtime": "^7.22.5", "@babel/preset-env": "^7.22.5", "@babel/register": "^7.22.5", "@babel/runtime": "^7.22.5", "@types/fs-extra": "^11.0.1", "@types/node": "^18.14.5", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "@vue/babel-preset-jsx": "^1.4.0", "adm-zip": "^0.5.10", "autoprefixer": "^10.4.14", "babel-loader": "^9.1.2", "cfonts": "^2.10.0", "chalk": "^4.1.2", "copy-webpack-plugin": "^11.0.0", "core-js": "^3.31.0", "cross-env": "^7.0.3", "css-loader": "^6.8.1", "date-fns": "^2.30.0", "del": "^6.1.1", "dotenv": "^16.1.4", "electron": "22.3.27", "electron-builder": "^24.4.0", "electron-devtools-installer": "^3.2.0", "element-ui": "^2.15.13", "esbuild-loader": "^3.0.1", "eslint": "^7.32.0", "eslint-config-standard": "^14.1.1", "eslint-friendly-formatter": "^4.0.1", "eslint-plugin-html": "^6.2.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.3.1", "eslint-plugin-standard": "^5.0.0", "eslint-webpack-plugin": "^3.2.0", "extract-zip": "^2.0.1", "html-webpack-plugin": "^5.5.3", "listr2": "^5.0.7", "mini-css-extract-plugin": "2.7.6", "minimist": "^1.2.8", "node-loader": "^2.0.0", "nprogress": "^0.2.0", "pinia": "^2.0.33", "portfinder": "^1.0.32", "postcss": "^8.4.24", "postcss-loader": "^7.3.3", "sass": "^1.63.4", "sass-loader": "^13.3.2", "style-loader": "^3.3.3", "svg-sprite-loader": "^6.0.11", "terser-webpack-plugin": "^5.3.9", "vue": "^2.7.14", "vue-devtools": "^5.1.4", "vue-html-loader": "^1.2.4", "vue-i18n": "^8.27.1", "vue-loader": "15.10.1", "vue-router": "^3.6.5", "vue-style-loader": "^4.1.3", "vue-template-compiler": "^2.7.14", "webpack": "^5.87.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "webpack-hot-middleware": "^2.25.3", "webpack-merge": "^5.9.0"}}