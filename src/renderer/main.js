import { createApp } from 'vue'
import { ipcRenderer } from 'electron'
import { createPinia } from 'pinia'

import App from './App'
import router from './router'
import { usePermission } from './permission'
import directive from './directive'
// 分页组件
import Pagination from "@/components/Pagination";
// 打印插件
import Print from 'vue-print-nb'
// 引用element plus
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { getName, resetForm, addDateRange, parseTime } from '@/utils/fuint'
import 'element-plus/dist/index.css'
// 缓存插件
import cache from '@/plugins/cache'
// 日志
import { setupErrorHandler } from './error'
import { setupIcons } from './icons'
import '@/styles/index.scss'
import '@/styles/dark-mode.scss'
import '@/styles/fuint.scss'

// 引入 i18n 语言包
import { createI18n } from 'vue-i18n'
import loadLanguage from "./i18n"
const languages = loadLanguage()
const pinia = createPinia()

// 创建 i18n
const i18n = createI18n({
  locale: 'zh-CN', // 设置默认语言
  legacy: false, // 使用 Composition API 模式
  messages: languages, // 设置语言包
});

// 创建Vue应用实例
const app = createApp(App)

// 注册全局组件
app.component('Pagination', Pagination)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 全局属性
app.config.globalProperties.getName = getName
app.config.globalProperties.resetForm = resetForm
app.config.globalProperties.addDateRange = addDateRange
app.config.globalProperties.parseTime = parseTime
app.config.globalProperties.$cache = cache

if (!process.env.IS_WEB) {
   ipcRenderer.invoke("IsUseSysTitle").then(res => {
     if (!res) {
        require('@/styles/custom-title.scss')
     }
  });
}

// 使用插件
app.use(pinia) // 确保pinia在最先挂载
app.use(directive)
app.use(Print);
app.use(router)
app.use(i18n)
app.use(ElementPlus)

// 设置错误处理和图标
setupErrorHandler(app)
setupIcons(app)

usePermission() // 放在后面，确保加载顺序

// 挂载应用
app.mount('#app')

