export default function loadLanguage() {
    const context = require.context("./languages", false, /([a-z_]+)\.js$/i)

    const languages = context
        .keys()
        .map((key) => ({ key, name: key.match(/([a-z_-]+)\.js$/i)[1] }))
        .reduce(
            (languages, {key, name}) => {
                let lang;
                try {
                    // 引入 element-plus 语言包
                    lang = Object.assign(context(key).lang, require(`element-plus/dist/locale/${name}.mjs`).default);
                } catch(err) {
                    try {
                        // 尝试其他路径
                        lang = Object.assign(context(key).lang, require(`element-plus/lib/locale/lang/${name}`).default);
                    } catch(err2) {
                        lang = context(key).lang
                    }
                }
                return {
                    ...languages,
                    [name]: lang
                }
            },
            {}
        )

    return languages
}