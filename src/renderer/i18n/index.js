export default function loadLanguage() {
    const context = require.context("./languages", false, /([a-z_]+)\.js$/i)

    const languages = context
        .keys()
        .map((key) => ({ key, name: key.match(/([a-z_-]+)\.js$/i)[1] }))
        .reduce(
            (languages, {key, name}) => {
                let lang;
                // 暂时只使用自定义语言包，Element Plus会使用默认语言
                lang = context(key).lang
                return {
                    ...languages,
                    [name]: lang
                }
            },
            {}
        )

    return languages
}