import { nextTick } from 'vue'

// Vue3中错误处理需要在应用实例上配置
// 这个文件现在只是导出错误处理函数，在main.js中使用
export function setupErrorHandler(app) {
  app.config.errorHandler = function (err, vm, info) {
    nextTick(() => {
      if (process.env.NODE_ENV === 'development') {
        console.group('%c >>>>>> 错误信息 >>>>>>', 'color:red')
        console.log(`%c ${info}`, 'color:blue')
        console.groupEnd()
        console.group('%c >>>>>> 发生错误的Vue 实例对象 >>>>>>', 'color:green')
        console.log(vm)
        console.groupEnd()
        console.group('%c >>>>>> 发生错误的原因及位置 >>>>>>', 'color:red')
        console.error(err)
        console.groupEnd()
      }
    })
  }
}
