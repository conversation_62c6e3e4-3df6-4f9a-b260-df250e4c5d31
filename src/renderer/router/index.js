import { createRouter, createWebHashHistory } from 'vue-router'
import Layout from '@/layout'
// 引入路由表
import asyncRouterMap from './constantRouterMap'

export const constantRouterMap = [{
  path: '/',
  component: Layout,
  redirect: '/dashboard',
  name: '主页',
  meta: { hidden: true },
  children: [{
    path: 'dashboard',
    name: '总览',
    component: () => import('@/views/home')
  }]
}, {
  path: '/login',
  component: () => import('@/views/login'),
  meta: { hidden: true }
}, {
  path: '/cashier',
  component: () => import('@/views/cashier'),
  meta: { hidden: true }
} , {
  path: '/setting',
  component: () => import('@/views/setting'),
  meta: { hidden: true }
}, {
  path: '/serial-test',
  component: () => import('@/views/test/SerialPortTest'),
  meta: { hidden: true }
}]
export const asyncRoutes = asyncRouterMap

const createRouterInstance = () => createRouter({
  history: createWebHashHistory(),
  scrollBehavior: () => ({ top: 0 }),
  routes: constantRouterMap
})

export function resetRouter() {
  const newRouter = createRouterInstance()
  router.matcher = newRouter.matcher
}
const router = createRouterInstance()

export default router
